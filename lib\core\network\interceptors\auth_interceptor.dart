import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/services/auth_expiry_service.dart';
import 'package:flutter_demo/core/storage/local_storage.dart';
import 'package:flutter_demo/core/utils/logger.dart';

/// 认证拦截器
///
/// 处理token的添加、刷新和过期逻辑
class AuthInterceptor extends Interceptor {
  /// 日志标签
  static const String _tag = 'AuthInterceptor';

  /// 本地存储实例
  final LocalStorage _localStorage;

  /// 构造函数
  AuthInterceptor(this._localStorage);

  /// 不需要token的接口白名单
  /// 这些接口通常是在用户未登录或认证失效时使用的
  static const List<String> _noTokenRequiredPaths = [
    'userservice/v1/user/login',           // 登录接口
    'userservice/v1/user/register',       // 注册接口
    'userservice/v1/user/sendSmsCode',    // 发送验证码接口
    'userservice/v1/user/resetPassword',  // 重置密码接口
  ];

  @override
  Future<void> onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // 检查当前请求是否在白名单中
    final requestPath = _extractPath(options.uri.toString());
    final shouldSkipToken = _shouldSkipToken(requestPath);

    if (shouldSkipToken) {
      Logger.debug(_tag, '跳过token验证的接口: $requestPath');
    } else {
      // 获取本地存储的token
      final token = _localStorage.getString(AppConstants.tokenKey);

      // 如果token存在，添加到请求头
      if (token != null && token.isNotEmpty) {
        options.headers['token'] = 'Bearer $token';

        // 使用Logger记录认证信息（脱敏处理）
        final maskedToken = Logger.maskSensitiveInfo(token);
        Logger.debug(_tag, '添加认证令牌: Bearer $maskedToken');
      } else {
        Logger.debug(_tag, '未找到token，跳过添加认证头');
      }
    }

    super.onRequest(options, handler);
  }

  /// 提取请求路径
  ///
  /// 从完整的URL中提取相对路径部分
  String _extractPath(String fullUrl) {
    try {
      final uri = Uri.parse(fullUrl);
      // 移除开头的斜杠，统一格式
      return uri.path.startsWith('/') ? uri.path.substring(1) : uri.path;
    } on Exception catch (e) {
      Logger.warning(_tag, '解析URL失败: $fullUrl, 错误: $e');
      return fullUrl;
    }
  }

  /// 检查是否应该跳过token验证
  ///
  /// [path] 请求路径
  /// 返回true表示应该跳过token验证
  bool _shouldSkipToken(String path) {
    return _noTokenRequiredPaths.any((noTokenPath) => path.contains(noTokenPath));
  }

  @override
  Future<void> onError(DioException err, ErrorInterceptorHandler handler) async {
    // 处理401未授权错误
    if (err.response?.statusCode == 401) {
      Logger.debug(_tag, '检测到401未授权错误，开始处理认证失效');

      try {
        // 使用依赖注入容器中的AuthExpiryService实例
        final authExpiryService = GetIt.instance<AuthExpiryService>();

        // 直接处理认证失效，清除数据并跳转登录页面
        await authExpiryService.handleAuthExpiryWithoutContext(
          message: '登录已过期，请重新登录',
        );

        // 继续抛出错误，让上层知道请求失败了
        return handler.next(err);
      } on Exception catch (e, s) {
        Logger.error(
          _tag,
          '处理认证失效时发生错误',
          exception: e,
          stackTrace: s,
        );
        // 即使处理失败，也要继续抛出原始错误
        return handler.next(err);
      }
    }

    // 其他错误直接传递
    return handler.next(err);
  }

}
