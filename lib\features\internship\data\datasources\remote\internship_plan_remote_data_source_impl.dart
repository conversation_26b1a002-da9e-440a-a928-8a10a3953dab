/// -----
/// internship_plan_remote_data_source_impl.dart
/// 
/// 实习计划远程数据源实现
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:async';
import 'package:get_it/get_it.dart';
import '../../../../../core/network/dio_client.dart';
import '../../../../../core/error/exceptions/server_exception.dart';
import '../../../../../core/error/exceptions/network_exception.dart';
import '../../../../../core/services/auth_expiry_service.dart';
import '../../../../../core/utils/logger.dart';
import '../../../data/models/internship_plan_model.dart';
import '../../../data/models/internship_plan_list_item_model.dart';
import 'internship_plan_remote_data_source.dart';

/// 实习计划远程数据源实现
/// 
/// 实现从远程API获取实习计划数据的具体逻辑
class InternshipPlanRemoteDataSourceImpl implements InternshipPlanRemoteDataSource {
  final DioClient _dioClient;

  InternshipPlanRemoteDataSourceImpl(this._dioClient);

  @override
  Future<List<InternshipPlanModel>> getTeacherInternshipPlans() async {
    try {
      Logger.info('InternshipPlanRemoteDataSource', '开始获取教师实习计划列表');
      
      final response = await _dioClient.get(
        'internshipservice/v1/internship/plan/basic/listByTeacher',
      );

      Logger.debug('InternshipPlanRemoteDataSource', '获取实习计划列表响应: $response');

      if (response is List) {
        final plans = response
            .map((json) => InternshipPlanModel.fromJson(json as Map<String, dynamic>))
            .toList();
        
        Logger.info('InternshipPlanRemoteDataSource', '成功获取${plans.length}个实习计划');
        return plans;
      } else if (response is Map && response.containsKey('data')) {
        final data = response['data'];
        if (data is List) {
          final plans = data
              .map((json) => InternshipPlanModel.fromJson(json as Map<String, dynamic>))
              .toList();
          
          Logger.info('InternshipPlanRemoteDataSource', '成功获取${plans.length}个实习计划');
          return plans;
        } else {
          Logger.warning('InternshipPlanRemoteDataSource', '响应数据格式不正确，data字段不是数组');
          return [];
        }
      } else {
        Logger.warning('InternshipPlanRemoteDataSource', '响应数据格式不正确');
        return [];
      }
    } catch (e) {
      Logger.error('InternshipPlanRemoteDataSource', '获取实习计划列表失败: $e');

      // 检查是否是认证失效错误
      if (e is ServerException && e.message.contains('登陆信息失效')) {
        Logger.warning('InternshipPlanRemoteDataSource', '检测到认证失效，触发认证失效处理');

        // 使用依赖注入容器中的AuthExpiryService实例
        final authExpiryService = GetIt.instance<AuthExpiryService>();

        // 异步处理认证失效，不阻塞当前流程
        unawaited(authExpiryService.handleAuthExpiryWithoutContext(
          message: '登录已过期，请重新登录',
        ).catchError((error) {
          Logger.error('InternshipPlanRemoteDataSource', '处理认证失效时发生错误: $error');
        }));
      }

      if (e is ServerException || e is NetworkException) {
        rethrow;
      } else {
        throw ServerException('获取实习计划列表失败: $e');
      }
    }
  }

  @override
  Future<InternshipPlanModel> getInternshipPlanDetail(String id) async {
    Logger.info('InternshipPlanRemoteDataSource', '开始获取实习计划详情，ID: $id');

    try {
      final response = await _dioClient.get(
        '/internshipservice/v1/internship/plan/basic/detail',
        queryParameters: {'id': id},
      );

      Logger.debug('InternshipPlanRemoteDataSource', '获取实习计划详情响应: $response');

      if (response != null) {
        final planModel = InternshipPlanModel.fromJson(response);
        Logger.info('InternshipPlanRemoteDataSource', '成功获取实习计划详情');
        return planModel;
      } else {
        throw ServerException('实习计划详情数据为空');
      }
    } catch (e) {
      Logger.error('InternshipPlanRemoteDataSource', '获取实习计划详情失败: $e');

      // 检查是否是认证失效错误
      if (e is ServerException && e.message.contains('登陆信息失效')) {
        Logger.warning('InternshipPlanRemoteDataSource', '检测到认证失效，触发认证失效处理');

        // 使用依赖注入容器中的AuthExpiryService实例
        final authExpiryService = GetIt.instance<AuthExpiryService>();

        // 异步处理认证失效，不阻塞当前流程
        unawaited(authExpiryService.handleAuthExpiryWithoutContext(
          message: '登录已过期，请重新登录',
        ).catchError((error) {
          Logger.error('InternshipPlanRemoteDataSource', '处理认证失效时发生错误: $error');
        }));
      }

      if (e is ServerException || e is NetworkException) {
        rethrow;
      } else {
        throw ServerException('获取实习计划详情失败: $e');
      }
    }
  }

  @override
  Future<List<InternshipPlanListItemModel>> getInternshipPlanList() async {
    try {
      Logger.info('InternshipPlanRemoteDataSource', '开始获取实习计划列表');

      final response = await _dioClient.get(
        'internshipservice/v1/internship/query/planList',
      );

      Logger.debug('InternshipPlanRemoteDataSource', '获取实习计划列表响应: $response');

      if (response is List) {
        final plans = response
            .map((json) => InternshipPlanListItemModel.fromJson(json as Map<String, dynamic>))
            .toList();

        Logger.info('InternshipPlanRemoteDataSource', '成功获取${plans.length}个实习计划');
        return plans;
      } else {
        Logger.warning('InternshipPlanRemoteDataSource', '响应数据格式不正确');
        return [];
      }
    } catch (e) {
      Logger.error('InternshipPlanRemoteDataSource', '获取实习计划列表失败: $e');

      // 检查是否是认证失效错误
      if (e is ServerException && e.message.contains('登陆信息失效')) {
        Logger.warning('InternshipPlanRemoteDataSource', '检测到认证失效，触发认证失效处理');

        // 使用依赖注入容器中的AuthExpiryService实例
        final authExpiryService = GetIt.instance<AuthExpiryService>();

        // 异步处理认证失效，不阻塞当前流程
        unawaited(authExpiryService.handleAuthExpiryWithoutContext(
          message: '登录已过期，请重新登录',
        ).catchError((error) {
          Logger.error('InternshipPlanRemoteDataSource', '处理认证失效时发生错误: $error');
        }));
      }

      if (e is ServerException || e is NetworkException) {
        rethrow;
      } else {
        throw ServerException('获取实习计划列表失败: $e');
      }
    }
  }
}
